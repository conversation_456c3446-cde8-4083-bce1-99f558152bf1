#!/usr/bin/env python3
"""
测试时钟检测器问题
验证无时钟进程时是否错误返回True
"""
import sys
import subprocess
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from pages.base.app_detector import AppDetector, AppType
from core.logger import log


def check_actual_clock_process():
    """检查实际的时钟进程"""
    print("🔍 检查实际的时钟进程状态")
    
    try:
        # 检查进程列表
        result = subprocess.run(
            ["adb", "shell", "ps", "|", "grep", "deskclock"],
            capture_output=True,
            text=True,
            timeout=10,
            shell=True
        )
        
        if result.returncode == 0 and result.stdout.strip():
            print(f"   ✅ 找到时钟进程:")
            for line in result.stdout.strip().split('\n'):
                print(f"     {line}")
            return True
        else:
            print("   ❌ 未找到时钟进程")
            return False
            
    except Exception as e:
        print(f"   ❌ 检查进程失败: {e}")
        return False


def test_clock_detection_methods():
    """测试时钟检测的各个方法"""
    print("\n🔍 测试时钟检测的各个方法")
    
    detector = AppDetector()
    clock_detector = detector.get_detector(AppType.CLOCK)
    
    if not clock_detector:
        print("   ❌ 无法获取时钟检测器")
        return
    
    print("   检测方法结果:")
    
    # 测试活动状态检查
    try:
        activity_result = clock_detector._check_activity_status()
        print(f"   - 活动状态检查: {'✅' if activity_result else '❌'}")
        
        if activity_result:
            # 获取详细的活动输出进行分析
            result = subprocess.run(
                ["adb", "shell", "dumpsys", "activity", "activities"],
                capture_output=True,
                text=True,
                timeout=10,
                encoding='utf-8',
                errors='ignore'
            )
            
            if result.returncode == 0:
                output = result.stdout
                packages = clock_detector.get_package_names()
                keywords = clock_detector.get_keywords()
                
                print("     详细分析:")
                
                # 检查包名匹配
                for package in packages:
                    if package in output:
                        print(f"       包名匹配: {package}")
                        # 检查是否真的是RESUMED状态
                        if clock_detector._verify_activity_resumed(output, package):
                            print(f"         状态: RESUMED ✅")
                        else:
                            print(f"         状态: 非RESUMED ⚠️")
                
                # 检查关键词匹配
                for keyword in keywords:
                    if keyword.lower() in output.lower():
                        print(f"       关键词匹配: {keyword}")
                        # 计算匹配次数
                        count = output.lower().count(keyword.lower())
                        print(f"         出现次数: {count}")
        
    except Exception as e:
        print(f"   - 活动状态检查失败: {e}")
    
    # 测试焦点窗口检查
    try:
        focus_result = clock_detector._check_focus_window()
        print(f"   - 焦点窗口检查: {'✅' if focus_result else '❌'}")
    except Exception as e:
        print(f"   - 焦点窗口检查失败: {e}")
    
    # 测试进程状态检查
    try:
        process_result = clock_detector._check_process_status()
        print(f"   - 进程状态检查: {'✅' if process_result else '❌'}")
    except Exception as e:
        print(f"   - 进程状态检查失败: {e}")


def test_clock_app_detection():
    """测试时钟应用检测"""
    print("\n⏰ 测试时钟应用检测")
    
    detector = AppDetector()
    
    # 使用原始方法检测
    clock_status = detector.check_app_opened(AppType.CLOCK)
    print(f"   时钟应用状态: {'✅ 运行中' if clock_status else '❌ 未运行'}")
    
    return clock_status


def main():
    """主测试函数"""
    print("🚀 时钟检测器问题诊断")
    print("=" * 50)
    
    # 1. 检查实际进程状态
    actual_process = check_actual_clock_process()
    
    # 2. 测试检测方法
    test_clock_detection_methods()
    
    # 3. 测试整体检测结果
    detected_status = test_clock_app_detection()
    
    # 4. 对比结果
    print("\n" + "=" * 50)
    print("📊 结果对比:")
    print(f"   实际进程状态: {'有进程' if actual_process else '无进程'}")
    print(f"   检测器结果: {'检测到' if detected_status else '未检测到'}")
    
    if not actual_process and detected_status:
        print("   ❌ 问题确认: 无进程但检测器返回True")
        print("   🔧 需要优化检测逻辑")
    elif actual_process and detected_status:
        print("   ✅ 正常: 有进程且检测器正确识别")
    elif not actual_process and not detected_status:
        print("   ✅ 正常: 无进程且检测器正确识别")
    else:
        print("   ⚠️ 异常: 有进程但检测器未识别")
    
    print("\n🎉 诊断完成!")


if __name__ == '__main__':
    main()
