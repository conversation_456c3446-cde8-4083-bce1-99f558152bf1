#!/usr/bin/env python3
"""
修复项目中所有相对路径导入为绝对路径导入
按照 from core.logger import log 的格式统一导入路径
"""
import os
import re
import sys
from pathlib import Path
from typing import List, Tuple, Dict


class RelativeImportFixer:
    """相对导入修复器"""
    
    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.fixed_files = []
        self.errors = []
        
        # 定义相对导入模式
        self.relative_import_patterns = [
            r'from\s+\.+\s*(\w+(?:\.\w+)*)\s+import\s+(.+)',  # from .module import something
            r'from\s+\.+(\w+(?:\.\w+)*)\s+import\s+(.+)',    # from ..module import something
            r'import\s+\.+(\w+(?:\.\w+)*)',                   # import .module
        ]
        
        # 定义需要跳过的文件和目录
        self.skip_patterns = [
            '__pycache__',
            '.git',
            '.pytest_cache',
            'node_modules',
            'venv',
            'env',
            '.venv',
            'build',
            'dist',
            '*.pyc',
            '*.pyo',
            '*.egg-info',
        ]
    
    def should_skip_file(self, file_path: Path) -> bool:
        """判断是否应该跳过文件"""
        file_str = str(file_path)
        for pattern in self.skip_patterns:
            if pattern in file_str:
                return True
        return False
    
    def find_python_files(self) -> List[Path]:
        """查找所有Python文件"""
        python_files = []
        
        for file_path in self.project_root.rglob("*.py"):
            if not self.should_skip_file(file_path):
                python_files.append(file_path)
        
        return python_files
    
    def analyze_relative_imports(self, content: str) -> List[Tuple[str, int]]:
        """分析文件中的相对导入"""
        relative_imports = []
        lines = content.split('\n')
        
        for line_num, line in enumerate(lines, 1):
            line = line.strip()
            if not line or line.startswith('#'):
                continue
                
            for pattern in self.relative_import_patterns:
                if re.search(pattern, line):
                    relative_imports.append((line, line_num))
                    break
        
        return relative_imports
    
    def convert_relative_to_absolute(self, file_path: Path, relative_import: str) -> str:
        """将相对导入转换为绝对导入"""
        try:
            # 获取文件相对于项目根目录的路径
            relative_file_path = file_path.relative_to(self.project_root)
            file_dir = relative_file_path.parent
            
            # 解析相对导入
            if 'from .' in relative_import:
                # 处理 from .module import something 格式
                match = re.match(r'from\s+(\.+)(\w+(?:\.\w+)*)\s+import\s+(.+)', relative_import)
                if match:
                    dots, module_path, imports = match.groups()
                    
                    # 计算绝对路径
                    if dots == '.':
                        # 同级目录
                        absolute_module = str(file_dir / module_path).replace('/', '.').replace('\\', '.')
                    elif dots == '..':
                        # 上级目录
                        absolute_module = str(file_dir.parent / module_path).replace('/', '.').replace('\\', '.')
                    else:
                        # 多级上级目录
                        levels = len(dots)
                        target_dir = file_dir
                        for _ in range(levels):
                            target_dir = target_dir.parent
                        absolute_module = str(target_dir / module_path).replace('/', '.').replace('\\', '.')
                    
                    return f"from {absolute_module} import {imports}"
                
                # 处理 from . import something 格式
                match = re.match(r'from\s+(\.+)\s+import\s+(.+)', relative_import)
                if match:
                    dots, imports = match.groups()
                    
                    if dots == '.':
                        # 从当前目录导入
                        absolute_module = str(file_dir).replace('/', '.').replace('\\', '.')
                    else:
                        # 从上级目录导入
                        levels = len(dots)
                        target_dir = file_dir
                        for _ in range(levels):
                            target_dir = target_dir.parent
                        absolute_module = str(target_dir).replace('/', '.').replace('\\', '.')
                    
                    return f"from {absolute_module} import {imports}"
            
            # 处理 import .module 格式
            match = re.match(r'import\s+(\.+)(\w+(?:\.\w+)*)', relative_import)
            if match:
                dots, module_path = match.groups()
                
                if dots == '.':
                    absolute_module = str(file_dir / module_path).replace('/', '.').replace('\\', '.')
                else:
                    levels = len(dots)
                    target_dir = file_dir
                    for _ in range(levels):
                        target_dir = target_dir.parent
                    absolute_module = str(target_dir / module_path).replace('/', '.').replace('\\', '.')
                
                return f"import {absolute_module}"
            
            return relative_import
            
        except Exception as e:
            self.errors.append(f"转换失败 {file_path}: {e}")
            return relative_import
    
    def fix_file(self, file_path: Path) -> bool:
        """修复单个文件的相对导入"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            relative_imports = self.analyze_relative_imports(content)
            
            if not relative_imports:
                return False
            
            print(f"\n🔧 修复文件: {file_path}")
            print(f"   找到 {len(relative_imports)} 个相对导入")
            
            # 逐行替换相对导入
            lines = content.split('\n')
            changes_made = []
            
            for import_line, line_num in relative_imports:
                absolute_import = self.convert_relative_to_absolute(file_path, import_line)
                if absolute_import != import_line:
                    lines[line_num - 1] = lines[line_num - 1].replace(import_line, absolute_import)
                    changes_made.append(f"   ✅ 第{line_num}行: {import_line} → {absolute_import}")
            
            if changes_made:
                # 写回文件
                new_content = '\n'.join(lines)
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(new_content)
                
                for change in changes_made:
                    print(change)
                
                self.fixed_files.append(str(file_path))
                return True
            
            return False
            
        except Exception as e:
            error_msg = f"修复文件失败 {file_path}: {e}"
            self.errors.append(error_msg)
            print(f"❌ {error_msg}")
            return False
    
    def fix_all_files(self) -> Dict[str, int]:
        """修复所有文件的相对导入"""
        print("🚀 开始修复项目中的相对路径导入")
        print("=" * 60)
        
        python_files = self.find_python_files()
        print(f"📁 找到 {len(python_files)} 个Python文件")
        
        fixed_count = 0
        total_files = len(python_files)
        
        for file_path in python_files:
            if self.fix_file(file_path):
                fixed_count += 1
        
        print("\n" + "=" * 60)
        print("📊 修复完成统计:")
        print(f"   总文件数: {total_files}")
        print(f"   修复文件数: {fixed_count}")
        print(f"   错误数: {len(self.errors)}")
        
        if self.errors:
            print("\n❌ 错误列表:")
            for error in self.errors:
                print(f"   {error}")
        
        if self.fixed_files:
            print("\n✅ 修复的文件:")
            for file_path in self.fixed_files:
                print(f"   {file_path}")
        
        return {
            'total_files': total_files,
            'fixed_files': fixed_count,
            'errors': len(self.errors)
        }


def main():
    """主函数"""
    # 获取项目根目录
    current_dir = Path(__file__).parent
    project_root = current_dir
    
    print(f"项目根目录: {project_root}")
    
    # 创建修复器并执行修复
    fixer = RelativeImportFixer(str(project_root))
    results = fixer.fix_all_files()
    
    print(f"\n🎉 修复完成!")
    print(f"修复了 {results['fixed_files']} 个文件中的相对导入")


if __name__ == '__main__':
    main()
