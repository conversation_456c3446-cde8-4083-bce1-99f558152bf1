#!/usr/bin/env python3
"""
验证项目中所有绝对路径导入是否正常工作
"""
import sys
import traceback
from pathlib import Path


def test_core_imports():
    """测试核心模块导入"""
    print("🔍 测试核心模块导入...")
    
    try:
        from core.logger import log
        print("   ✅ core.logger 导入成功")
    except Exception as e:
        print(f"   ❌ core.logger 导入失败: {e}")
        return False
    
    try:
        from core.base_driver import BaseDriver
        print("   ✅ core.base_driver 导入成功")
    except Exception as e:
        print(f"   ❌ core.base_driver 导入失败: {e}")
        return False
    
    try:
        from core.base_page import BasePage
        print("   ✅ core.base_page 导入成功")
    except Exception as e:
        print(f"   ❌ core.base_page 导入失败: {e}")
        return False
    
    return True


def test_app_detector_imports():
    """测试应用检测器导入"""
    print("\n🔍 测试应用检测器导入...")
    
    try:
        from pages.base.app_detector import AppDetector, AppType
        print("   ✅ pages.base.app_detector 导入成功")
    except Exception as e:
        print(f"   ❌ pages.base.app_detector 导入失败: {e}")
        return False
    
    # 测试各个检测器
    detectors = [
        'weather_detector',
        'camera_detector', 
        'clock_detector',
        'contacts_detector',
        'facebook_detector',
        'music_detector',
        'maps_detector',
        'playstore_detector',
        'settings_detector'
    ]
    
    for detector_name in detectors:
        try:
            module_path = f"pages.base.detectors.{detector_name}"
            __import__(module_path)
            print(f"   ✅ {module_path} 导入成功")
        except Exception as e:
            print(f"   ❌ {module_path} 导入失败: {e}")
            return False
    
    return True


def test_page_imports():
    """测试页面模块导入"""
    print("\n🔍 测试页面模块导入...")
    
    try:
        from pages.apps.ella.dialogue_page import EllaDialoguePage
        print("   ✅ pages.apps.ella.dialogue_page 导入成功")
    except Exception as e:
        print(f"   ❌ pages.apps.ella.dialogue_page 导入失败: {e}")
        return False
    
    try:
        from pages.base.common_page import CommonPage
        print("   ✅ pages.base.common_page 导入成功")
    except Exception as e:
        print(f"   ❌ pages.base.common_page 导入失败: {e}")
        return False
    
    return True


def test_utils_imports():
    """测试工具模块导入"""
    print("\n🔍 测试工具模块导入...")
    
    try:
        from utils.tts_utils import generate_audio_file
        print("   ✅ utils.tts_utils 导入成功")
    except Exception as e:
        print(f"   ❌ utils.tts_utils 导入失败: {e}")
        return False
    
    try:
        from utils.file_utils import FileUtils
        print("   ✅ utils.file_utils 导入成功")
    except Exception as e:
        print(f"   ❌ utils.file_utils 导入失败: {e}")
        return False
    
    try:
        from utils.yaml_utils import YamlUtils
        print("   ✅ utils.yaml_utils 导入成功")
    except Exception as e:
        print(f"   ❌ utils.yaml_utils 导入失败: {e}")
        return False
    
    return True


def test_detector_functionality():
    """测试检测器功能"""
    print("\n🔍 测试检测器功能...")
    
    try:
        from pages.base.app_detector import AppDetector, AppType
        
        # 创建检测器实例
        detector = AppDetector()
        print("   ✅ AppDetector 实例创建成功")
        
        # 测试获取检测器
        weather_detector = detector.get_detector(AppType.WEATHER)
        if weather_detector:
            print("   ✅ 天气检测器获取成功")
        else:
            print("   ❌ 天气检测器获取失败")
            return False
        
        # 测试包名获取
        package_names = weather_detector.get_package_names()
        if package_names:
            print(f"   ✅ 天气应用包名获取成功: {len(package_names)} 个")
        else:
            print("   ❌ 天气应用包名获取失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"   ❌ 检测器功能测试失败: {e}")
        traceback.print_exc()
        return False


def test_import_patterns():
    """测试导入模式是否符合要求"""
    print("\n🔍 检查项目代码导入模式...")

    # 只检查项目代码，排除第三方库
    project_dirs = ['pages', 'core', 'utils', 'testcases', 'tools', 'config']
    relative_imports_found = []

    for dir_name in project_dirs:
        dir_path = Path(dir_name)
        if not dir_path.exists():
            continue

        python_files = list(dir_path.rglob('*.py'))

        for file_path in python_files:
            if '__pycache__' in str(file_path):
                continue

            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()

                lines = content.split('\n')
                for line_num, line in enumerate(lines, 1):
                    line = line.strip()
                    if line.startswith('from .') or line.startswith('from ..'):
                        relative_imports_found.append((file_path, line_num, line))
            except:
                continue

    if relative_imports_found:
        print("   ❌ 项目代码中仍然存在相对导入:")
        for file_path, line_num, line in relative_imports_found:
            print(f"      {file_path}:{line_num} - {line}")
        return False
    else:
        print("   ✅ 项目代码中未发现相对导入")
        return True


def main():
    """主测试函数"""
    print("🚀 验证绝对路径导入")
    print("=" * 50)
    
    tests = [
        test_core_imports,
        test_app_detector_imports,
        test_page_imports,
        test_utils_imports,
        test_detector_functionality,
        test_import_patterns
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"   ❌ 测试异常: {e}")
            traceback.print_exc()
    
    print("\n" + "=" * 50)
    print("📊 测试结果:")
    print(f"   通过: {passed}/{total}")
    print(f"   成功率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("\n🎉 所有导入测试通过！绝对路径导入配置正确。")
        return True
    else:
        print(f"\n⚠️ 有 {total-passed} 个测试失败，需要进一步检查。")
        return False


if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
