#!/usr/bin/env python3
"""
精确测试闹钟检测器
验证不同闹钟状态的检测精度
"""
import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from pages.base.detectors.alarm_detector import AlarmDetector
from core.logger import log


def test_alarm_precision():
    """精确测试闹钟状态检测"""
    print("🔍 精确闹钟状态检测测试")
    print("=" * 50)
    
    detector = AlarmDetector()
    
    # 测试1: 检查闹钟是否正在响铃
    print("\n🔔 测试1: 检查闹钟是否正在响铃")
    try:
        is_ringing = detector.check_alarm_ringing()
        print(f"   闹钟响铃状态: {'🔔 正在响铃' if is_ringing else '🔇 未响铃'}")
    except Exception as e:
        print(f"   ❌ 测试失败: {e}")
    
    # 测试2: 检查是否有启用的闹钟
    print("\n⏰ 测试2: 检查是否有启用的闹钟")
    try:
        has_enabled = detector.check_alarm_status()
        print(f"   启用闹钟状态: {'✅ 有启用闹钟' if has_enabled else '❌ 无启用闹钟'}")
    except Exception as e:
        print(f"   ❌ 测试失败: {e}")
    
    # 测试3: 详细分析各个检测方法
    print("\n🔍 测试3: 详细检测方法分析")
    try:
        print("   各检测方法结果:")
        
        # 数据库检查
        has_db = detector._check_alarm_database()
        print(f"   - 数据库启用闹钟: {'✅' if has_db else '❌'}")
        
        # 系统下一个闹钟
        has_next = detector._check_next_alarm()
        print(f"   - 系统下一个闹钟: {'✅' if has_next else '❌'}")
        
        # 验证下一个闹钟是否启用
        if has_next:
            next_enabled = detector._verify_next_alarm_enabled()
            print(f"   - 下一个闹钟已启用: {'✅' if next_enabled else '❌'}")
        
        # 活跃进程检查
        has_process = detector._check_alarm_process()
        print(f"   - 活跃闹钟进程: {'✅' if has_process else '❌'}")
        
    except Exception as e:
        print(f"   ❌ 测试失败: {e}")
    
    # 测试4: 获取详细闹钟信息
    print("\n📋 测试4: 获取详细闹钟信息")
    try:
        alarms = detector.get_alarm_list()
        print(f"   总闹钟数: {len(alarms)}")
        
        enabled_count = 0
        disabled_count = 0
        
        if alarms:
            print("   闹钟详情:")
            for i, alarm in enumerate(alarms, 1):
                enabled = alarm.get('enabled', False)
                if enabled:
                    enabled_count += 1
                else:
                    disabled_count += 1
                    
                status = "启用" if enabled else "禁用"
                time_str = alarm.get('time', 'N/A')
                label = alarm.get('label', '无标签')
                source = alarm.get('source', 'unknown')
                print(f"     {i}. {time_str} - {status} - {label} ({source})")
        
        print(f"   启用闹钟: {enabled_count} 个")
        print(f"   禁用闹钟: {disabled_count} 个")
        
    except Exception as e:
        print(f"   ❌ 测试失败: {e}")
    
    # 测试5: 逻辑一致性验证
    print("\n✅ 测试5: 逻辑一致性验证")
    try:
        is_ringing = detector.check_alarm_ringing()
        has_enabled = detector.check_alarm_status()
        alarms = detector.get_alarm_list()
        enabled_alarms = [a for a in alarms if a.get('enabled', False)]
        next_alarm = detector.get_next_alarm_info()
        
        print(f"   闹钟响铃: {is_ringing}")
        print(f"   有启用闹钟: {has_enabled}")
        print(f"   启用闹钟数量: {len(enabled_alarms)}")
        print(f"   有下一个闹钟: {next_alarm is not None}")
        
        # 逻辑验证
        issues = []
        
        if is_ringing and not has_enabled:
            issues.append("闹钟正在响铃但检测为无启用闹钟")
        
        if has_enabled and len(enabled_alarms) == 0 and next_alarm is None:
            issues.append("检测有启用闹钟但找不到具体闹钟")
        
        if not has_enabled and len(enabled_alarms) > 0:
            issues.append("有启用闹钟但检测为无启用闹钟")
        
        if issues:
            print("   ⚠️ 发现逻辑问题:")
            for issue in issues:
                print(f"     - {issue}")
        else:
            print("   ✅ 逻辑一致，检测准确")
            
    except Exception as e:
        print(f"   ❌ 测试失败: {e}")


def main():
    """主测试函数"""
    print("🚀 闹钟检测精度测试")
    print("=" * 60)
    
    test_alarm_precision()
    
    print("\n" + "=" * 60)
    print("📊 测试总结:")
    
    detector = AlarmDetector()
    
    # 最终状态
    try:
        is_ringing = detector.check_alarm_ringing()
        has_enabled = detector.check_alarm_status()
        
        print(f"   闹钟响铃状态: {'🔔 响铃中' if is_ringing else '🔇 未响铃'}")
        print(f"   启用闹钟状态: {'✅ 有启用' if has_enabled else '❌ 无启用'}")
        
        if not has_enabled and not is_ringing:
            print("   ✅ 优化成功: 当前无启用闹钟且未响铃，正确返回False")
        elif has_enabled and not is_ringing:
            print("   ℹ️ 当前有启用的闹钟但未响铃")
        elif is_ringing:
            print("   🔔 当前闹钟正在响铃")
        
    except Exception as e:
        print(f"   ❌ 最终状态检查失败: {e}")
    
    print("\n🎉 测试完成!")


if __name__ == '__main__':
    main()
