#!/usr/bin/env python3
"""
最终验证音乐检测器优化效果
"""
import sys
import subprocess
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from pages.base.app_detector import AppDetector, AppType
from core.logger import log


def check_magicshow_process_detailed():
    """详细检查magicshow进程"""
    print("🔍 详细检查magicshow进程")
    
    try:
        # 获取所有进程
        result = subprocess.run(
            ["adb", "shell", "ps"],
            capture_output=True,
            text=True,
            timeout=10
        )
        
        if result.returncode == 0:
            lines = result.stdout.split('\n')
            magicshow_processes = []
            
            for line in lines:
                if 'magicshow' in line:
                    magicshow_processes.append(line.strip())
            
            if magicshow_processes:
                print(f"   ✅ 找到 {len(magicshow_processes)} 个magicshow进程:")
                for process in magicshow_processes:
                    print(f"     {process}")
                return True
            else:
                print("   ❌ 未找到magicshow进程")
                return False
        else:
            print("   ❌ 获取进程列表失败")
            return False
            
    except Exception as e:
        print(f"   ❌ 检查失败: {e}")
        return False


def test_music_detection_final():
    """最终测试音乐检测"""
    print("\n🎵 最终测试音乐检测")
    
    detector = AppDetector()
    
    # 测试音乐应用检测
    music_status = detector.check_app_opened(AppType.MUSIC)
    print(f"   音乐应用检测结果: {'✅ 运行中' if music_status else '❌ 未运行'}")
    
    # 获取音乐检测器进行详细测试
    music_detector = detector.get_detector(AppType.MUSIC)
    if music_detector:
        print("\n   详细检测方法结果:")
        
        # 活动状态
        activity_result = music_detector._check_activity_status()
        print(f"   - 活动状态: {'✅' if activity_result else '❌'}")
        
        # 焦点窗口
        focus_result = music_detector._check_focus_window()
        print(f"   - 焦点窗口: {'✅' if focus_result else '❌'}")
        
        # 进程状态
        process_result = music_detector._check_process_status()
        print(f"   - 进程状态: {'✅' if process_result else '❌'}")
        
        # 检查包名配置
        packages = music_detector.get_package_names()
        print(f"\n   配置的包名: {len(packages)} 个")
        for pkg in packages:
            if 'magicshow' in pkg or 'visha' in pkg:
                print(f"     ✅ {pkg}")
    
    return music_status


def test_different_app_types():
    """测试不同应用类型的检测差异"""
    print("\n🔍 测试不同应用类型的检测差异")
    
    detector = AppDetector()
    
    # 测试时钟应用（应该严格检测前台）
    clock_status = detector.check_app_opened(AppType.CLOCK)
    print(f"   时钟应用: {'✅ 运行中' if clock_status else '❌ 未运行'}")
    
    # 测试音乐应用（允许后台运行）
    music_status = detector.check_app_opened(AppType.MUSIC)
    print(f"   音乐应用: {'✅ 运行中' if music_status else '❌ 未运行'}")
    
    print("\n   检测策略差异:")
    print("   - 时钟应用: 严格前台检测（避免后台服务误判）")
    print("   - 音乐应用: 允许后台运行（音乐播放场景）")


def main():
    """主测试函数"""
    print("🚀 音乐检测器优化最终验证")
    print("=" * 50)
    
    # 1. 详细检查进程
    has_process = check_magicshow_process_detailed()
    
    # 2. 测试音乐检测
    detected_status = test_music_detection_final()
    
    # 3. 测试不同应用类型
    test_different_app_types()
    
    # 4. 最终结果
    print("\n" + "=" * 50)
    print("📊 最终验证结果:")
    print(f"   Magicshow进程存在: {'✅' if has_process else '❌'}")
    print(f"   音乐检测器结果: {'✅ 检测到' if detected_status else '❌ 未检测到'}")
    
    if has_process and detected_status:
        print("   🎉 优化成功: Visha进程启动时正确返回True")
    elif has_process and not detected_status:
        print("   ❌ 仍有问题: 有进程但检测器返回False")
    elif not has_process and not detected_status:
        print("   ✅ 正常: 无进程且检测器正确返回False")
    else:
        print("   ⚠️ 异常: 无进程但检测器返回True")
    
    print("\n🎉 验证完成!")


if __name__ == '__main__':
    main()
