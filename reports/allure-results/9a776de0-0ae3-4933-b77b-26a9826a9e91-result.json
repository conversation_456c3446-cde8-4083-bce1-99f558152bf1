{"name": "测试navigate from beijing to no. 310 huangzhao road, pudong new area, shanghai能正常执行", "status": "passed", "description": "navigate from beijing to no. 310 huangzhao road, pudong new area, shanghai", "steps": [{"name": "执行命令: navigate from beijing to no. 310 huangzhao road, pudong new area, shanghai", "status": "passed", "steps": [{"name": "执行命令: navigate from beijing to no. 310 huangzhao road, pudong new area, shanghai", "status": "passed", "start": 1753416894603, "stop": 1753416910474}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "7f3fa2c0-27c6-470d-aa33-9f5a14662a40-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "6b56904a-f78d-4ddf-ba0a-c0b52cfd4123-attachment.png", "type": "image/png"}], "start": 1753416910474, "stop": 1753416910787}], "start": 1753416894603, "stop": 1753416910788}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1753416910788, "stop": 1753416910791}, {"name": "验证应用已打开", "status": "passed", "start": 1753416910791, "stop": 1753416910791}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "098a602c-0fa6-42f2-8f47-4a6d044ad3f3-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "93303a6f-0910-4a4b-974c-72a1771a7a4d-attachment.png", "type": "image/png"}], "start": 1753416910791, "stop": 1753416911135}], "attachments": [{"name": "stdout", "source": "58a87b16-194f-477b-86d0-111f697022ff-attachment.txt", "type": "text/plain"}], "start": 1753416894603, "stop": 1753416911136, "uuid": "1dc96533-b64e-49eb-8a0d-386270f724bb", "historyId": "f622c7c4831272dc58cb99e6af8d9943", "testCaseId": "f622c7c4831272dc58cb99e6af8d9943", "fullName": "testcases.test_ella.third_coupling.test_navigate_from_beijing_to_shanghai.TestEllaNavigateFromBeijingShanghai#test_navigate_from_beijing_to_shanghai", "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.third_coupling"}, {"name": "suite", "value": "test_navigate_from_beijing_to_shanghai"}, {"name": "subSuite", "value": "TestEllaNavigateFromBeijingShanghai"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "50052-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.third_coupling.test_navigate_from_beijing_to_shanghai"}]}