{"name": "测试switch to power saving mode能正常执行", "status": "passed", "description": "switch to power saving mode", "steps": [{"name": "执行命令: switch to power saving mode", "status": "passed", "steps": [{"name": "执行命令: switch to power saving mode", "status": "passed", "start": 1753419015367, "stop": 1753419025681}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "b8b2588d-929e-438e-bd41-ca5121cba830-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "593ab880-137a-46cc-8651-84288aa1e9fc-attachment.png", "type": "image/png"}], "start": 1753419025681, "stop": 1753419026083}], "start": 1753419015367, "stop": 1753419026084}, {"name": "验证响应包含在期望中", "status": "passed", "start": 1753419026084, "stop": 1753419026087}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "dcd8aca1-97f2-45d2-ad1a-cb468f929299-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "1938f5c8-4cd2-4ade-bbd9-abdb893dba16-attachment.png", "type": "image/png"}], "start": 1753419026087, "stop": 1753419026447}], "attachments": [{"name": "stdout", "source": "542c1c62-35d8-4a8c-b1a7-382a274f24d0-attachment.txt", "type": "text/plain"}], "start": 1753419015367, "stop": 1753419026448, "uuid": "56c27f18-feb0-4c82-a888-82177f32ff16", "historyId": "8fd5e40ff38046a9fe7fe83ba6136249", "testCaseId": "8fd5e40ff38046a9fe7fe83ba6136249", "fullName": "testcases.test_ella.third_coupling.test_switch_to_power_saving_mode.TestEllaSwitchToPowerSavingMode#test_switch_to_power_saving_mode", "labels": [{"name": "severity", "value": "critical"}, {"name": "story", "value": "Ella语音助手基础指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.third_coupling"}, {"name": "suite", "value": "test_switch_to_power_saving_mode"}, {"name": "subSuite", "value": "TestEllaSwitchToPowerSavingMode"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "50052-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.third_coupling.test_switch_to_power_saving_mode"}]}