{"name": "测试switch charging modes能正常执行", "status": "passed", "description": "switch charging modes", "steps": [{"name": "执行命令: switch charging modes", "status": "passed", "steps": [{"name": "执行命令: switch charging modes", "status": "passed", "start": 1753418794756, "stop": 1753418805968}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "56be600d-090b-4e6b-b56f-8a88dff4e631-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "273d8925-2ee7-42ab-9919-e9a94efccf97-attachment.png", "type": "image/png"}], "start": 1753418805968, "stop": 1753418806297}], "start": 1753418794756, "stop": 1753418806298}, {"name": "验证响应包含在期望中", "status": "passed", "start": 1753418806298, "stop": 1753418806303}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "3962a92b-7066-4478-a087-1ee3d53cebc9-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "1eb4b92d-bf99-414b-adb3-f006ef6e9cca-attachment.png", "type": "image/png"}], "start": 1753418806303, "stop": 1753418806673}], "attachments": [{"name": "stdout", "source": "f86dd90b-4667-46c2-8c14-a1dd83c036bb-attachment.txt", "type": "text/plain"}], "start": 1753418794755, "stop": 1753418806673, "uuid": "fa5ae600-1ecb-4a0c-882a-167e7be81354", "historyId": "eab84c8d9454644261f5e07341993e63", "testCaseId": "eab84c8d9454644261f5e07341993e63", "fullName": "testcases.test_ella.third_coupling.test_switch_charging_modes.TestEllaSwitchChargingModes#test_switch_charging_modes", "labels": [{"name": "severity", "value": "critical"}, {"name": "story", "value": "Ella语音助手基础指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.third_coupling"}, {"name": "suite", "value": "test_switch_charging_modes"}, {"name": "subSuite", "value": "TestEllaSwitchChargingModes"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "50052-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.third_coupling.test_switch_charging_modes"}]}