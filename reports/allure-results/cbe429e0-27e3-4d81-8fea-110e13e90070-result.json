{"name": "测试switch magic voice to <PERSON><PERSON>能正常执行", "status": "passed", "description": "switch magic voice to Mango", "steps": [{"name": "执行命令: switch magic voice to Mango", "status": "passed", "steps": [{"name": "执行命令: switch magic voice to Mango", "status": "passed", "start": 1753418847131, "stop": 1753418857505}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "d44ff9e5-8212-4a31-ace3-5eb971417163-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "f96a3435-b849-4e3d-b7b4-10b677b38d53-attachment.png", "type": "image/png"}], "start": 1753418857505, "stop": 1753418857875}], "start": 1753418847131, "stop": 1753418857876}, {"name": "验证响应包含在期望中", "status": "passed", "start": 1753418857876, "stop": 1753418857881}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "a9495e05-576e-4853-8c38-d93adeea2b16-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "ae5df4b3-7deb-49d0-a839-0c269a4e39b8-attachment.png", "type": "image/png"}], "start": 1753418857881, "stop": 1753418858290}], "attachments": [{"name": "stdout", "source": "34efa2e4-ec36-4db6-b22b-e0af2f99a245-attachment.txt", "type": "text/plain"}], "start": 1753418847131, "stop": 1753418858292, "uuid": "c7a048d5-7da8-40c0-b893-d90db728e932", "historyId": "77f7a75d1fb0221269048ba90dc85f58", "testCaseId": "77f7a75d1fb0221269048ba90dc85f58", "fullName": "testcases.test_ella.third_coupling.test_switch_magic_voice_to_mango.TestEllaSwitchMagicVoiceToMango#test_switch_magic_voice_to_mango", "labels": [{"name": "severity", "value": "critical"}, {"name": "story", "value": "Ella语音助手基础指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.third_coupling"}, {"name": "suite", "value": "test_switch_magic_voice_to_mango"}, {"name": "subSuite", "value": "TestEllaSwitchMagicVoiceToMango"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "50052-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.third_coupling.test_switch_magic_voice_to_mango"}]}