{"name": "测试switch to default mode能正常执行", "status": "passed", "description": "switch to default mode", "steps": [{"name": "执行命令: switch to default mode", "status": "passed", "steps": [{"name": "执行命令: switch to default mode", "status": "passed", "start": 1753418873064, "stop": 1753418890725}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "0006953d-cf28-4de6-86f1-1c703ce98d55-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "5b010ad6-fc4f-4c9f-8250-897c396bf3e8-attachment.png", "type": "image/png"}], "start": 1753418890725, "stop": 1753418891113}], "start": 1753418873064, "stop": 1753418891114}, {"name": "验证响应包含在期望中", "status": "passed", "start": 1753418891114, "stop": 1753418891118}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "8ab07c8f-dc92-4753-ae0a-2f76ffb66e0c-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "a5166a81-46e9-4694-85a4-0c1ea3130388-attachment.png", "type": "image/png"}], "start": 1753418891118, "stop": 1753418891491}], "attachments": [{"name": "stdout", "source": "339adad7-eab7-45e2-b15a-a33220785af2-attachment.txt", "type": "text/plain"}], "start": 1753418873064, "stop": 1753418891492, "uuid": "08d281a0-66ac-4b79-a8af-1de851bbb842", "historyId": "26cb6f8ba689334e5a942af5045d47a4", "testCaseId": "26cb6f8ba689334e5a942af5045d47a4", "fullName": "testcases.test_ella.third_coupling.test_switch_to_default_mode.TestEllaSwitchToDefaultMode#test_switch_to_default_mode", "labels": [{"name": "severity", "value": "critical"}, {"name": "story", "value": "Ella语音助手基础指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.third_coupling"}, {"name": "suite", "value": "test_switch_to_default_mode"}, {"name": "subSuite", "value": "TestEllaSwitchToDefaultMode"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "50052-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.third_coupling.test_switch_to_default_mode"}]}