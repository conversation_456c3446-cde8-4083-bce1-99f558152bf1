{"name": "测试Switch to Hyper Charge能正常执行", "status": "passed", "description": "Switch to Hyper Charge", "steps": [{"name": "执行命令: Switch to Hyper Charge", "status": "passed", "steps": [{"name": "执行命令: Switch to Hyper Charge", "status": "passed", "start": 1753418964159, "stop": 1753418974206}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "23781567-ff75-4850-a325-c65b32e2aa5e-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "8e09534a-1de6-4dbc-aedd-13a983a9f324-attachment.png", "type": "image/png"}], "start": 1753418974206, "stop": 1753418974584}], "start": 1753418964159, "stop": 1753418974585}, {"name": "验证响应包含在期望中", "status": "passed", "start": 1753418974585, "stop": 1753418974588}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "572c20e0-60df-4684-89cc-925c008a769e-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "e01444bc-0916-4087-9e6c-27515be9fc2e-attachment.png", "type": "image/png"}], "start": 1753418974589, "stop": 1753418974959}], "attachments": [{"name": "stdout", "source": "c3788f9b-0d3f-466f-a3b2-6de903cef92c-attachment.txt", "type": "text/plain"}], "start": 1753418964159, "stop": 1753418974960, "uuid": "e839c876-13d0-4623-99a2-0586c825e581", "historyId": "cc16e6af6c17da447c32eeb71c66d71c", "testCaseId": "cc16e6af6c17da447c32eeb71c66d71c", "fullName": "testcases.test_ella.third_coupling.test_switch_to_hyper_charge.TestEllaSwitchToHyperCharge#test_switch_to_hyper_charge", "labels": [{"name": "severity", "value": "critical"}, {"name": "story", "value": "Ella语音助手基础指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.third_coupling"}, {"name": "suite", "value": "test_switch_to_hyper_charge"}, {"name": "subSuite", "value": "TestEllaSwitchToHyperCharge"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "50052-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.third_coupling.test_switch_to_hyper_charge"}]}