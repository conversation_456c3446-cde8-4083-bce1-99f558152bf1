{"name": "测试navigation to the lucky能正常执行", "status": "passed", "description": "navigation to the lucky", "steps": [{"name": "执行命令: navigation to the lucky", "status": "passed", "steps": [{"name": "执行命令: navigation to the lucky", "status": "passed", "start": 1753418679933, "stop": 1753418699656}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "e0fbfa22-b156-4e99-bb81-d43043fb2c4a-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "3feccfe1-58ba-40c2-bc53-59e9a42416b9-attachment.png", "type": "image/png"}], "start": 1753418699656, "stop": 1753418700043}], "start": 1753418679933, "stop": 1753418700044}, {"name": "验证应用已打开", "status": "passed", "start": 1753418700044, "stop": 1753418700044}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "2429870d-afb0-46e2-b882-2d7743b34c0f-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "1b6f1625-d2dc-4ce6-9014-a125425d347d-attachment.png", "type": "image/png"}], "start": 1753418700044, "stop": 1753418700350}], "attachments": [{"name": "stdout", "source": "18e00e5f-c306-4c58-8960-0ade34cc556f-attachment.txt", "type": "text/plain"}], "start": 1753418679933, "stop": 1753418700351, "uuid": "4ad77a85-6168-4c0e-9786-1c2f0e105178", "historyId": "75c56947a7b1061f7ec858fb20919b50", "testCaseId": "75c56947a7b1061f7ec858fb20919b50", "fullName": "testcases.test_ella.third_coupling.test_navigation_to_the_lucky.TestEllaNavigationToTheLucky#test_navigation_to_the_lucky", "labels": [{"name": "severity", "value": "critical"}, {"name": "story", "value": "Ella语音助手基础指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.third_coupling"}, {"name": "suite", "value": "test_navigation_to_the_lucky"}, {"name": "subSuite", "value": "TestEllaNavigationToTheLucky"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "50052-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.third_coupling.test_navigation_to_the_lucky"}]}