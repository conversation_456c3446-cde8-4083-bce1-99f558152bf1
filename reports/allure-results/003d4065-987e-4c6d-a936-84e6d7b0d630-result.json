{"name": "测试smart charge能正常执行", "status": "passed", "description": "smart charge", "steps": [{"name": "执行命令: smart charge", "status": "passed", "steps": [{"name": "执行命令: smart charge", "status": "passed", "start": 1753418767809, "stop": 1753418778832}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "84b2ce17-96ab-48b1-b974-c0780a52dfb2-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "2c3cb1a4-3f5a-4026-a7b1-0dc2835e8a2d-attachment.png", "type": "image/png"}], "start": 1753418778833, "stop": 1753418779195}], "start": 1753418767809, "stop": 1753418779196}, {"name": "验证响应包含在期望中", "status": "passed", "start": 1753418779196, "stop": 1753418779200}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "4a94bd8f-a9c0-4ffa-825b-dc18b247fdf0-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "3a05bc17-fd33-434a-b489-52179197aa55-attachment.png", "type": "image/png"}], "start": 1753418779201, "stop": 1753418779550}], "attachments": [{"name": "stdout", "source": "8c3b2acb-3a14-4a3d-8087-8a0ba9476e84-attachment.txt", "type": "text/plain"}], "start": 1753418767809, "stop": 1753418779551, "uuid": "c4d79c51-33e1-4db6-8d58-4cc77843469a", "historyId": "547e7a68915f4e05c1ef0182ac744dc1", "testCaseId": "547e7a68915f4e05c1ef0182ac744dc1", "fullName": "testcases.test_ella.third_coupling.test_smart_charge.TestEllaSmartCharge#test_smart_charge", "labels": [{"name": "severity", "value": "critical"}, {"name": "story", "value": "Ella语音助手基础指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.third_coupling"}, {"name": "suite", "value": "test_smart_charge"}, {"name": "subSuite", "value": "TestEllaSmartCharge"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "50052-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.third_coupling.test_smart_charge"}]}