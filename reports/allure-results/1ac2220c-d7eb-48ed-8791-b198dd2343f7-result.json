{"name": "测试order a takeaway能正常执行", "status": "passed", "description": "order a takeaway", "steps": [{"name": "执行命令: order a takeaway", "status": "passed", "steps": [{"name": "执行命令: order a takeaway", "status": "passed", "start": 1753418740608, "stop": 1753418752251}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "527cc3e8-337c-4ce6-a2e8-064377a7c2ec-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "ef778532-035f-4e74-83e8-c0fa3a19184e-attachment.png", "type": "image/png"}], "start": 1753418752251, "stop": 1753418752610}], "start": 1753418740608, "stop": 1753418752611}, {"name": "验证响应包含在期望中", "status": "passed", "start": 1753418752611, "stop": 1753418752614}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "0bdf474e-9dfa-417d-878b-b427f08ef300-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "c6c18966-75fb-462c-a234-f51417d976b9-attachment.png", "type": "image/png"}], "start": 1753418752614, "stop": 1753418752917}], "attachments": [{"name": "stdout", "source": "044666f7-4902-41e1-9877-1bad68f1cc8e-attachment.txt", "type": "text/plain"}], "start": 1753418740608, "stop": 1753418752918, "uuid": "7a249b2f-e92e-4c7a-b5d3-0dd29f985e44", "historyId": "de5ff490f92fc399976d91fe0edc371e", "testCaseId": "de5ff490f92fc399976d91fe0edc371e", "fullName": "testcases.test_ella.third_coupling.test_order_a_takeaway.TestEllaOrderATakeaway#test_order_a_takeaway", "labels": [{"name": "severity", "value": "critical"}, {"name": "story", "value": "Ella语音助手基础指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.third_coupling"}, {"name": "suite", "value": "test_order_a_takeaway"}, {"name": "subSuite", "value": "TestEllaOrderATakeaway"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "50052-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.third_coupling.test_order_a_takeaway"}]}