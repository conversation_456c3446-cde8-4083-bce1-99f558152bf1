{"name": "测试Switch Magic Voice to Grace能正常执行", "status": "passed", "description": "Switch Magic Voice to Grace", "steps": [{"name": "执行命令: Switch Magic Voice to Grace", "status": "passed", "steps": [{"name": "执行命令: Switch Magic Voice to Grace", "status": "passed", "start": 1753418821374, "stop": 1753418831436}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "0b066b62-b171-4b8e-82a2-090808746da5-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "0a07cb7a-a28d-4e4a-af37-c0a2d6e935ad-attachment.png", "type": "image/png"}], "start": 1753418831436, "stop": 1753418831757}], "start": 1753418821374, "stop": 1753418831758}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1753418831758, "stop": 1753418831762}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "beb477d0-de6e-4da9-9791-5d2cb8445487-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "a1a186da-d49c-4d8b-9e14-c234f21fab2a-attachment.png", "type": "image/png"}], "start": 1753418831762, "stop": 1753418832176}], "attachments": [{"name": "stdout", "source": "18297de6-fc66-4322-b2fc-6f529592e8bb-attachment.txt", "type": "text/plain"}], "start": 1753418821374, "stop": 1753418832177, "uuid": "ec871167-f504-4839-9604-202710d8bc8f", "historyId": "a7f998e2471e40f11da78b97bb88dfba", "testCaseId": "a7f998e2471e40f11da78b97bb88dfba", "fullName": "testcases.test_ella.third_coupling.test_switch_magic_voice_to_grace.TestEllaSwitchMagicVoiceGrace#test_switch_magic_voice_to_grace", "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.third_coupling"}, {"name": "suite", "value": "test_switch_magic_voice_to_grace"}, {"name": "subSuite", "value": "TestEllaSwitchMagicVoiceGrace"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "50052-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.third_coupling.test_switch_magic_voice_to_grace"}]}