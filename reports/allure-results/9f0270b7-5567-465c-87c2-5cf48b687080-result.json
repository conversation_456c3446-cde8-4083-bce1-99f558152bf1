{"name": "测试switch to smart charge能正常执行", "status": "passed", "description": "switch to smart charge", "steps": [{"name": "执行命令: switch to smart charge", "status": "passed", "steps": [{"name": "执行命令: switch to smart charge", "status": "passed", "start": 1753419041353, "stop": 1753419050736}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "e9b2dc0c-cf59-442e-9e0b-5958bf468111-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "a4f2955c-53ca-4f64-83f0-7f97a7448a98-attachment.png", "type": "image/png"}], "start": 1753419050736, "stop": 1753419051108}], "start": 1753419041353, "stop": 1753419051109}, {"name": "验证响应包含在期望中", "status": "passed", "start": 1753419051109, "stop": 1753419051113}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "59a9b86a-39d1-420b-b2ae-40264501f064-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "1ada58c5-0584-44c0-8b88-ba52b2a5d35a-attachment.png", "type": "image/png"}], "start": 1753419051113, "stop": 1753419051479}], "attachments": [{"name": "stdout", "source": "39a87d7d-187e-4f77-a5c8-983c2eeec1ca-attachment.txt", "type": "text/plain"}], "start": 1753419041353, "stop": 1753419051481, "uuid": "92041fff-6744-4798-bb25-bc47fa627e14", "historyId": "6b1dfe37fc0819486f6cda64c12143be", "testCaseId": "6b1dfe37fc0819486f6cda64c12143be", "fullName": "testcases.test_ella.third_coupling.test_switch_to_smart_charge.TestEllaSwitchToSmartCharge#test_switch_to_smart_charge", "labels": [{"name": "severity", "value": "critical"}, {"name": "story", "value": "Ella语音助手基础指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.third_coupling"}, {"name": "suite", "value": "test_switch_to_smart_charge"}, {"name": "subSuite", "value": "TestEllaSwitchToSmartCharge"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "50052-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.third_coupling.test_switch_to_smart_charge"}]}