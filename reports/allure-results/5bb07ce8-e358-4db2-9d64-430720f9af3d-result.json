{"name": "测试switch to flash notification能正常执行", "status": "passed", "description": "switch to flash notification", "steps": [{"name": "执行命令: switch to flash notification", "status": "passed", "steps": [{"name": "执行命令: switch to flash notification", "status": "passed", "start": 1753418933416, "stop": 1753418948917}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "e56c207e-28e4-4de7-abaa-fe2d06eed41c-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "0f36fddd-c8ef-4de5-8746-5beb2ead0050-attachment.png", "type": "image/png"}], "start": 1753418948917, "stop": 1753418949170}], "start": 1753418933416, "stop": 1753418949171}, {"name": "验证响应包含在期望中", "status": "passed", "start": 1753418949171, "stop": 1753418949174}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "4df3d0d8-8f25-424e-9ce9-c937e0c442a3-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "6a6e5874-7902-4c8f-81a5-fdd94b920d20-attachment.png", "type": "image/png"}], "start": 1753418949174, "stop": 1753418949512}], "attachments": [{"name": "stdout", "source": "fd2b850e-0e7f-45a6-aa57-57680f771e91-attachment.txt", "type": "text/plain"}], "start": 1753418933416, "stop": 1753418949513, "uuid": "4dc8394d-8145-419b-8c34-1d81648aaf61", "historyId": "ed459b5d14edcd2b6a73764bc41c7e2d", "testCaseId": "ed459b5d14edcd2b6a73764bc41c7e2d", "fullName": "testcases.test_ella.third_coupling.test_switch_to_flash_notification.TestEllaSwitchToFlashNotification#test_switch_to_flash_notification", "labels": [{"name": "severity", "value": "critical"}, {"name": "story", "value": "Ella语音助手基础指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.third_coupling"}, {"name": "suite", "value": "test_switch_to_flash_notification"}, {"name": "subSuite", "value": "TestEllaSwitchToFlashNotification"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "50052-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.third_coupling.test_switch_to_flash_notification"}]}