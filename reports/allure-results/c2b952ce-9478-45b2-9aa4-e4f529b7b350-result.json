{"name": "测试Switch to Low-Temp Charge能正常执行", "status": "passed", "description": "Switch to Low-Temp Charge", "steps": [{"name": "执行命令: Switch to Low-Temp Charge", "status": "passed", "steps": [{"name": "执行命令: Switch to Low-Temp Charge", "status": "passed", "start": 1753418989792, "stop": 1753418999827}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "12e4c68c-add6-45db-882c-a5e46ba7c195-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "3a1ff105-75e2-4e83-a8ff-b5df6a9f9cc8-attachment.png", "type": "image/png"}], "start": 1753418999827, "stop": 1753419000180}], "start": 1753418989792, "stop": 1753419000181}, {"name": "验证响应包含在期望中", "status": "passed", "start": 1753419000181, "stop": 1753419000187}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "09b71a0a-0d2d-40cf-a68b-c8c3f2406bb5-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "6eee1211-df12-4534-87d9-89767ad99a14-attachment.png", "type": "image/png"}], "start": 1753419000187, "stop": 1753419000528}], "attachments": [{"name": "stdout", "source": "64e88183-ef3b-40ee-9a80-016537190dc0-attachment.txt", "type": "text/plain"}], "start": 1753418989791, "stop": 1753419000529, "uuid": "11d49efc-e7ac-4f31-85b6-3a3c72f4d63f", "historyId": "ee231dd22ce3bf43d8c81633728f58b1", "testCaseId": "ee231dd22ce3bf43d8c81633728f58b1", "fullName": "testcases.test_ella.third_coupling.test_switch_to_low_temp_charge.TestEllaSwitchToLowtempCharge#test_switch_to_low_temp_charge", "labels": [{"name": "severity", "value": "critical"}, {"name": "story", "value": "Ella语音助手基础指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.third_coupling"}, {"name": "suite", "value": "test_switch_to_low_temp_charge"}, {"name": "subSuite", "value": "TestEllaSwitchToLowtempCharge"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "50052-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.third_coupling.test_switch_to_low_temp_charge"}]}