{"name": "测试navigate from to red square能正常执行", "status": "broken", "statusDetails": {"message": "adbutils.errors.AdbError: device '13764254B4001229' not found", "trace": "self = <uiautomator2.core.AdbHTTPConnection object at 0x000001E5F5BA0130>\n\n    def connect(self):\n        try:\n>           self.sock = self.__device.create_connection(adbutils.Network.TCP, self.__port)\n                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n\nC:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\uiautomator2\\core.py:97: \n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\nC:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\adbutils\\_device_base.py:457: in create_connection\n    c = self.open_transport()\n        ^^^^^^^^^^^^^^^^^^^^^\nC:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\adbutils\\_device_base.py:87: in open_transport\n    c.check_okay()\n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\n\nself = <adbutils._adb.AdbConnection object at 0x000001E5F5BB0D70>\n\n    def check_okay(self):\n        data = self.read(4)\n        if data == _FAIL:\n>           raise AdbError(self.read_string_block())\nE           adbutils.errors.AdbError: device '13764254B4001229' not found\n\nC:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\adbutils\\_adb.py:192: AdbError\n\nThe above exception was the direct cause of the following exception:\n\nself = <uiautomator2.Device object at 0x000001E5F4383C50>, method = 'takeScreenshot', params = (1, 80), timeout = 300\n\n    def jsonrpc_call(self, method: str, params: Any = None, timeout: float = 10) -> Any:\n        \"\"\"Send jsonrpc call to uiautomator2 server\"\"\"\n        try:\n>           return _jsonrpc_call(self._dev, self._device_server_port, method, params, timeout, self._debug)\n                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n\nC:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\uiautomator2\\core.py:311: \n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\nC:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\uiautomator2\\core.py:173: in _jsonrpc_call\n    r = _http_request(dev, device_port, \"POST\", \"/jsonrpc/0\", payload, timeout=timeout, print_request=print_request)\n        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nC:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\uiautomator2\\core.py:139: in _http_request\n    conn.request(method, path, json.dumps(data), headers=headers)\nC:\\Python313\\Lib\\http\\client.py:1338: in request\n    self._send_request(method, url, body, headers, encode_chunked)\nC:\\Python313\\Lib\\http\\client.py:1384: in _send_request\n    self.endheaders(body, encode_chunked=encode_chunked)\nC:\\Python313\\Lib\\http\\client.py:1333: in endheaders\n    self._send_output(message_body, encode_chunked=encode_chunked)\nC:\\Python313\\Lib\\http\\client.py:1093: in _send_output\n    self.send(msg)\nC:\\Python313\\Lib\\http\\client.py:1037: in send\n    self.connect()\n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\n\nself = <uiautomator2.core.AdbHTTPConnection object at 0x000001E5F5BA0130>\n\n    def connect(self):\n        try:\n            self.sock = self.__device.create_connection(adbutils.Network.TCP, self.__port)\n        except adbutils.AdbError as e:\n>           raise HTTPError(f\"Unable to connect to uiautomator2 server: {e}\") from e\nE           uiautomator2.exceptions.HTTPError: Unable to connect to uiautomator2 server: device '13764254B4001229' not found\n\nC:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\uiautomator2\\core.py:99: HTTPError\n\nDuring handling of the above exception, another exception occurred:\n\nself = <testcases.test_ella.third_coupling.test_navigate_from_to_red_square.TestEllaNavigateFromRedSquare object at 0x000001E5F44F3890>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000001E5F5B1C2D0>\n\n    @allure.title(f\"测试{command}能正常执行\")\n    @allure.description(f\"{command}\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_navigate_from_to_red_square(self, ella_app):\n        f\"\"\"{self.command}\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n>           initial_status, final_status, response_text = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n\ntestcases\\test_ella\\third_coupling\\test_navigate_from_to_red_square.py:26: \n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\ntestcases\\test_ella\\base_ella_test.py:1484: in simple_command_test\n    self.take_screenshot(ella_app, \"test_completed\")\ntestcases\\test_ella\\base_ella_test.py:1281: in take_screenshot\n    screenshot_path = ella_app.screenshot(f\"{name}.png\")\n                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\ncore\\base_page.py:152: in screenshot\n    return driver_manager.screenshot(filename, use_test_class_dir)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\ncore\\base_driver.py:304: in screenshot\n    self.driver.screenshot(screenshot_path)\nC:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\uiautomator2\\__init__.py:77: in screenshot\n    base64_data = self.jsonrpc.takeScreenshot(1, 80)\n                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nC:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\uiautomator2\\base.py:158: in __call__\n    return self.server.jsonrpc_call(self.method, params, http_timeout)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nC:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\uiautomator2\\core.py:315: in jsonrpc_call\n    self.start_uiautomator()\nC:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\uiautomator2\\core.py:231: in start_uiautomator\n    self._setup_jar()\nC:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\uiautomator2\\core.py:242: in _setup_jar\n    if self._check_device_file_hash(jar_path, target_path):\n       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nC:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\uiautomator2\\core.py:255: in _check_device_file_hash\n    output = self._dev.shell([\"toybox\", \"md5sum\", remote_file])\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nC:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\adbutils\\_device_base.py:206: in shell\n    c = self.open_shell(cmdargs)\n        ^^^^^^^^^^^^^^^^^^^^^^^^\nC:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\adbutils\\_device_base.py:170: in open_shell\n    c = self.open_transport()\n        ^^^^^^^^^^^^^^^^^^^^^\nC:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\adbutils\\_device_base.py:87: in open_transport\n    c.check_okay()\n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\n\nself = <adbutils._adb.AdbConnection object at 0x000001E5F5BB2A50>\n\n    def check_okay(self):\n        data = self.read(4)\n        if data == _FAIL:\n>           raise AdbError(self.read_string_block())\nE           adbutils.errors.AdbError: device '13764254B4001229' not found\n\nC:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\adbutils\\_adb.py:192: AdbError"}, "description": "navigate from to red square", "steps": [{"name": "执行命令: navigate from to red square", "status": "broken", "statusDetails": {"message": "adbutils.errors.AdbError: device '13764254B4001229' not found\n", "trace": "  File \"D:\\aigc\\app_test\\testcases\\test_ella\\third_coupling\\test_navigate_from_to_red_square.py\", line 26, in test_navigate_from_to_red_square\n    initial_status, final_status, response_text = self.simple_command_test(\n                                                  ~~~~~~~~~~~~~~~~~~~~~~~~^\n        ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    )\n    ^\n  File \"D:\\aigc\\app_test\\testcases\\test_ella\\base_ella_test.py\", line 1484, in simple_command_test\n    self.take_screenshot(ella_app, \"test_completed\")\n    ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\aigc\\app_test\\testcases\\test_ella\\base_ella_test.py\", line 1281, in take_screenshot\n    screenshot_path = ella_app.screenshot(f\"{name}.png\")\n  File \"D:\\aigc\\app_test\\core\\base_page.py\", line 152, in screenshot\n    return driver_manager.screenshot(filename, use_test_class_dir)\n           ~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\aigc\\app_test\\core\\base_driver.py\", line 304, in screenshot\n    self.driver.screenshot(screenshot_path)\n    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\uiautomator2\\__init__.py\", line 77, in screenshot\n    base64_data = self.jsonrpc.takeScreenshot(1, 80)\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\uiautomator2\\base.py\", line 158, in __call__\n    return self.server.jsonrpc_call(self.method, params, http_timeout)\n           ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\uiautomator2\\core.py\", line 315, in jsonrpc_call\n    self.start_uiautomator()\n    ~~~~~~~~~~~~~~~~~~~~~~^^\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\uiautomator2\\core.py\", line 231, in start_uiautomator\n    self._setup_jar()\n    ~~~~~~~~~~~~~~~^^\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\uiautomator2\\core.py\", line 242, in _setup_jar\n    if self._check_device_file_hash(jar_path, target_path):\n       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\uiautomator2\\core.py\", line 255, in _check_device_file_hash\n    output = self._dev.shell([\"toybox\", \"md5sum\", remote_file])\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\adbutils\\_device_base.py\", line 206, in shell\n    c = self.open_shell(cmdargs)\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\adbutils\\_device_base.py\", line 170, in open_shell\n    c = self.open_transport()\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\adbutils\\_device_base.py\", line 87, in open_transport\n    c.check_okay()\n    ~~~~~~~~~~~~^^\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\adbutils\\_adb.py\", line 192, in check_okay\n    raise AdbError(self.read_string_block())\n"}, "steps": [{"name": "执行命令: navigate from to red square", "status": "passed", "start": 1753416925792, "stop": 1753418631272}, {"name": "记录测试结果", "status": "broken", "statusDetails": {"message": "adbutils.errors.AdbError: device '13764254B4001229' not found\n", "trace": "  File \"D:\\aigc\\app_test\\testcases\\test_ella\\base_ella_test.py\", line 1484, in simple_command_test\n    self.take_screenshot(ella_app, \"test_completed\")\n    ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\aigc\\app_test\\testcases\\test_ella\\base_ella_test.py\", line 1281, in take_screenshot\n    screenshot_path = ella_app.screenshot(f\"{name}.png\")\n  File \"D:\\aigc\\app_test\\core\\base_page.py\", line 152, in screenshot\n    return driver_manager.screenshot(filename, use_test_class_dir)\n           ~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\aigc\\app_test\\core\\base_driver.py\", line 304, in screenshot\n    self.driver.screenshot(screenshot_path)\n    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\uiautomator2\\__init__.py\", line 77, in screenshot\n    base64_data = self.jsonrpc.takeScreenshot(1, 80)\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\uiautomator2\\base.py\", line 158, in __call__\n    return self.server.jsonrpc_call(self.method, params, http_timeout)\n           ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\uiautomator2\\core.py\", line 315, in jsonrpc_call\n    self.start_uiautomator()\n    ~~~~~~~~~~~~~~~~~~~~~~^^\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\uiautomator2\\core.py\", line 231, in start_uiautomator\n    self._setup_jar()\n    ~~~~~~~~~~~~~~~^^\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\uiautomator2\\core.py\", line 242, in _setup_jar\n    if self._check_device_file_hash(jar_path, target_path):\n       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\uiautomator2\\core.py\", line 255, in _check_device_file_hash\n    output = self._dev.shell([\"toybox\", \"md5sum\", remote_file])\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\adbutils\\_device_base.py\", line 206, in shell\n    c = self.open_shell(cmdargs)\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\adbutils\\_device_base.py\", line 170, in open_shell\n    c = self.open_transport()\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\adbutils\\_device_base.py\", line 87, in open_transport\n    c.check_okay()\n    ~~~~~~~~~~~~^^\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\adbutils\\_adb.py\", line 192, in check_okay\n    raise AdbError(self.read_string_block())\n"}, "attachments": [{"name": "测试总结", "source": "bfe7eb50-7f96-41e9-a89e-8b1cd674586f-attachment.txt", "type": "text/plain"}], "start": 1753418631272, "stop": 1753418631740}], "start": 1753416925792, "stop": 1753418631803}], "attachments": [{"name": "stdout", "source": "09270715-efcf-4e07-9934-478fc6144d07-attachment.txt", "type": "text/plain"}], "start": 1753416925792, "stop": 1753418631825, "uuid": "47857056-4beb-4aa4-a305-de58fa95ab15", "historyId": "e8f03971277a71512b5ebaad612bc964", "testCaseId": "e8f03971277a71512b5ebaad612bc964", "fullName": "testcases.test_ella.third_coupling.test_navigate_from_to_red_square.TestEllaNavigateFromRedSquare#test_navigate_from_to_red_square", "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.third_coupling"}, {"name": "suite", "value": "test_navigate_from_to_red_square"}, {"name": "subSuite", "value": "TestEllaNavigateFromRedSquare"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "50052-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.third_coupling.test_navigate_from_to_red_square"}]}