#!/usr/bin/env python3
"""
测试Visha音乐检测器问题
验证com.transsion.magicshow进程启动时是否正确返回True
"""
import sys
import subprocess
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from pages.base.app_detector import AppDetector, AppType
from core.logger import log


def check_actual_visha_process():
    """检查实际的Visha进程"""
    print("🔍 检查实际的Visha进程状态")
    
    try:
        # 检查magicshow进程
        result = subprocess.run(
            ["adb", "shell", "ps", "|", "grep", "magicshow"],
            capture_output=True,
            text=True,
            timeout=10,
            shell=True
        )
        
        has_magicshow = False
        if result.returncode == 0 and result.stdout.strip():
            print(f"   ✅ 找到magicshow进程:")
            for line in result.stdout.strip().split('\n'):
                print(f"     {line}")
            has_magicshow = True
        else:
            print("   ❌ 未找到magicshow进程")
        
        # 检查visha相关进程
        result2 = subprocess.run(
            ["adb", "shell", "ps", "|", "grep", "visha"],
            capture_output=True,
            text=True,
            timeout=10,
            shell=True
        )
        
        has_visha = False
        if result2.returncode == 0 and result2.stdout.strip():
            print(f"   ✅ 找到visha进程:")
            for line in result2.stdout.strip().split('\n'):
                print(f"     {line}")
            has_visha = True
        else:
            print("   ❌ 未找到visha进程")
        
        # 检查应用是否在前台
        result3 = subprocess.run(
            ["adb", "shell", "dumpsys", "window", "windows", "|", "grep", "mCurrentFocus"],
            capture_output=True,
            text=True,
            timeout=10,
            shell=True
        )
        
        has_focus = False
        if result3.returncode == 0 and result3.stdout.strip():
            focus_line = result3.stdout.strip()
            if "magicshow" in focus_line.lower() or "visha" in focus_line.lower():
                print(f"   ✅ 音乐应用在前台: {focus_line}")
                has_focus = True
            else:
                print(f"   ❌ 音乐应用不在前台: {focus_line}")
        
        return has_magicshow or has_visha, has_focus
            
    except Exception as e:
        print(f"   ❌ 检查进程失败: {e}")
        return False, False


def test_music_detection_methods():
    """测试音乐检测的各个方法"""
    print("\n🔍 测试音乐检测的各个方法")
    
    detector = AppDetector()
    music_detector = detector.get_detector(AppType.MUSIC)
    
    if not music_detector:
        print("   ❌ 无法获取音乐检测器")
        return
    
    print("   检测方法结果:")
    
    # 测试活动状态检查
    try:
        activity_result = music_detector._check_activity_status()
        print(f"   - 活动状态检查: {'✅' if activity_result else '❌'}")
        
        if not activity_result:
            print("     分析活动状态详情...")
            result = subprocess.run(
                ["adb", "shell", "dumpsys", "activity", "activities", "|", "grep", "-A", "5", "-B", "5", "magicshow"],
                capture_output=True,
                text=True,
                timeout=10,
                shell=True
            )
            
            if result.returncode == 0 and result.stdout.strip():
                print(f"     找到magicshow活动信息:")
                for line in result.stdout.strip().split('\n')[:10]:  # 只显示前10行
                    print(f"       {line}")
            else:
                print("     未找到magicshow活动信息")
        
    except Exception as e:
        print(f"   - 活动状态检查失败: {e}")
    
    # 测试焦点窗口检查
    try:
        focus_result = music_detector._check_focus_window()
        print(f"   - 焦点窗口检查: {'✅' if focus_result else '❌'}")
    except Exception as e:
        print(f"   - 焦点窗口检查失败: {e}")
    
    # 测试进程状态检查
    try:
        process_result = music_detector._check_process_status()
        print(f"   - 进程状态检查: {'✅' if process_result else '❌'}")
        
        if not process_result:
            print("     分析进程状态详情...")
            result = subprocess.run(
                ["adb", "shell", "ps"],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            if result.returncode == 0:
                lines = result.stdout.split('\n')
                packages = music_detector.get_package_names()
                
                found_processes = []
                for line in lines:
                    line = line.strip()
                    if not line:
                        continue
                    
                    for package in packages:
                        if package in line:
                            found_processes.append((package, line))
                            print(f"       找到包含 {package} 的进程: {line}")
                            # 验证这个进程
                            is_running = music_detector._verify_process_running(line, package)
                            print(f"       验证结果: {'前台应用' if is_running else '后台服务/非前台'}")
                            
                            if not is_running:
                                # 进一步分析为什么验证失败
                                is_foreground = music_detector._verify_foreground_app_process(package)
                                print(f"       前台验证: {'是前台应用' if is_foreground else '非前台应用'}")
                
                if not found_processes:
                    print("       未找到任何音乐应用进程")
        
    except Exception as e:
        print(f"   - 进程状态检查失败: {e}")


def test_music_app_detection():
    """测试音乐应用检测"""
    print("\n🎵 测试音乐应用检测")
    
    detector = AppDetector()
    
    # 使用原始方法检测
    music_status = detector.check_app_opened(AppType.MUSIC)
    print(f"   音乐应用状态: {'✅ 运行中' if music_status else '❌ 未运行'}")
    
    return music_status


def main():
    """主测试函数"""
    print("🚀 Visha音乐检测器问题诊断")
    print("=" * 50)
    
    # 1. 检查实际进程状态
    has_process, has_focus = check_actual_visha_process()
    
    # 2. 测试检测方法
    test_music_detection_methods()
    
    # 3. 测试整体检测结果
    detected_status = test_music_app_detection()
    
    # 4. 对比结果
    print("\n" + "=" * 50)
    print("📊 结果对比:")
    print(f"   实际进程状态: {'有进程' if has_process else '无进程'}")
    print(f"   前台状态: {'在前台' if has_focus else '不在前台'}")
    print(f"   检测器结果: {'检测到' if detected_status else '未检测到'}")
    
    if has_process and not detected_status:
        print("   ❌ 问题确认: 有进程但检测器返回False")
        print("   🔧 需要优化检测逻辑")
        
        if not has_focus:
            print("   💡 可能原因: 应用在后台运行，前台验证过于严格")
    elif has_process and detected_status:
        print("   ✅ 正常: 有进程且检测器正确识别")
    elif not has_process and not detected_status:
        print("   ✅ 正常: 无进程且检测器正确识别")
    else:
        print("   ⚠️ 异常: 无进程但检测器识别到")
    
    print("\n🎉 诊断完成!")


if __name__ == '__main__':
    main()
