#!/usr/bin/env python3
"""
测试优化后的时钟检测器
验证无时钟进程时是否正确返回False
"""
import sys
import subprocess
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from pages.base.app_detector import AppDetector, AppType
from core.logger import log


def check_actual_clock_process():
    """检查实际的时钟进程"""
    print("🔍 检查实际的时钟进程状态")
    
    try:
        # 方法1: 检查进程列表
        result = subprocess.run(
            ["adb", "shell", "ps", "|", "grep", "deskclock"],
            capture_output=True,
            text=True,
            timeout=10,
            shell=True
        )
        
        has_process = False
        if result.returncode == 0 and result.stdout.strip():
            print(f"   ✅ 找到时钟进程:")
            for line in result.stdout.strip().split('\n'):
                print(f"     {line}")
            has_process = True
        else:
            print("   ❌ 未找到时钟进程")
        
        # 方法2: 检查应用是否在前台
        result2 = subprocess.run(
            ["adb", "shell", "dumpsys", "window", "windows", "|", "grep", "mCurrentFocus"],
            capture_output=True,
            text=True,
            timeout=10,
            shell=True
        )
        
        has_focus = False
        if result2.returncode == 0 and result2.stdout.strip():
            focus_line = result2.stdout.strip()
            if "deskclock" in focus_line.lower():
                print(f"   ✅ 时钟应用在前台: {focus_line}")
                has_focus = True
            else:
                print(f"   ❌ 时钟应用不在前台: {focus_line}")
        
        return has_process or has_focus
            
    except Exception as e:
        print(f"   ❌ 检查进程失败: {e}")
        return False


def test_optimized_detection_methods():
    """测试优化后的检测方法"""
    print("\n🔍 测试优化后的检测方法")
    
    detector = AppDetector()
    clock_detector = detector.get_detector(AppType.CLOCK)
    
    if not clock_detector:
        print("   ❌ 无法获取时钟检测器")
        return
    
    print("   检测方法结果:")
    
    # 测试活动状态检查
    try:
        activity_result = clock_detector._check_activity_status()
        print(f"   - 活动状态检查: {'✅' if activity_result else '❌'}")
        
        if activity_result:
            print("     ⚠️ 活动状态检查返回True，分析原因...")
            # 这里可以添加更详细的分析
        
    except Exception as e:
        print(f"   - 活动状态检查失败: {e}")
    
    # 测试焦点窗口检查
    try:
        focus_result = clock_detector._check_focus_window()
        print(f"   - 焦点窗口检查: {'✅' if focus_result else '❌'}")
    except Exception as e:
        print(f"   - 焦点窗口检查失败: {e}")
    
    # 测试进程状态检查
    try:
        process_result = clock_detector._check_process_status()
        print(f"   - 进程状态检查: {'✅' if process_result else '❌'}")
        
        if process_result:
            print("     ⚠️ 进程状态检查返回True，分析原因...")
            # 获取详细的进程输出进行分析
            result = subprocess.run(
                ["adb", "shell", "ps"],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            if result.returncode == 0:
                lines = result.stdout.split('\n')
                packages = clock_detector.get_package_names()
                
                for line in lines:
                    line = line.strip()
                    if not line:
                        continue
                    
                    for package in packages:
                        if package in line:
                            print(f"       找到包含 {package} 的进程行: {line}")
                            # 验证这个进程是否真的在运行
                            is_running = clock_detector._verify_process_running(line, package)
                            print(f"       验证结果: {'运行中' if is_running else '非运行状态'}")
        
    except Exception as e:
        print(f"   - 进程状态检查失败: {e}")


def test_overall_detection():
    """测试整体检测结果"""
    print("\n⏰ 测试整体检测结果")
    
    detector = AppDetector()
    
    # 使用优化后的方法检测
    clock_status = detector.check_app_opened(AppType.CLOCK)
    print(f"   时钟应用状态: {'✅ 运行中' if clock_status else '❌ 未运行'}")
    
    return clock_status


def compare_before_after():
    """对比优化前后的效果"""
    print("\n📊 对比优化效果")
    
    # 实际状态
    actual_status = check_actual_clock_process()
    
    # 检测器结果
    detected_status = test_overall_detection()
    
    print(f"\n   实际状态: {'有时钟应用' if actual_status else '无时钟应用'}")
    print(f"   检测结果: {'检测到' if detected_status else '未检测到'}")
    
    if actual_status == detected_status:
        print("   ✅ 检测准确")
        return True
    else:
        print("   ❌ 检测不准确")
        return False


def main():
    """主测试函数"""
    print("🚀 时钟检测器优化验证")
    print("=" * 50)
    
    # 1. 检查实际状态
    actual_status = check_actual_clock_process()
    
    # 2. 测试优化后的检测方法
    test_optimized_detection_methods()
    
    # 3. 测试整体检测
    detected_status = test_overall_detection()
    
    # 4. 对比结果
    print("\n" + "=" * 50)
    print("📊 最终结果:")
    print(f"   实际状态: {'有时钟应用' if actual_status else '无时钟应用'}")
    print(f"   检测结果: {'检测到' if detected_status else '未检测到'}")
    
    if not actual_status and not detected_status:
        print("   ✅ 优化成功: 无时钟进程时正确返回False")
    elif actual_status and detected_status:
        print("   ✅ 检测正确: 有时钟进程时正确返回True")
    elif not actual_status and detected_status:
        print("   ❌ 仍有问题: 无时钟进程但返回True")
        print("   🔧 需要进一步优化")
    else:
        print("   ⚠️ 检测遗漏: 有时钟进程但返回False")
    
    print("\n🎉 验证完成!")


if __name__ == '__main__':
    main()
