"""
修复检测器文件的导入路径问题
"""
import os
import glob
from pathlib import Path

def create_robust_import_header():
    """创建健壮的导入头部"""
    return '''"""
{detector_name}
"""
from typing import List
import sys
import os

# 添加父目录到路径以便导入
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

# 多种导入策略
try:
    from pages.base.app_detector import BaseAppDetector, AppType
except ImportError:
    try:
        from pages.base.app_detector import BaseAppDetector, AppType
    except ImportError:
        # 最后的备用方案
        import importlib.util
        app_detector_path = os.path.join(parent_dir, 'app_detector.py')
        if os.path.exists(app_detector_path):
            spec = importlib.util.spec_from_file_location("app_detector", app_detector_path)
            app_detector_module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(app_detector_module)
            BaseAppDetector = app_detector_module.BaseAppDetector
            AppType = app_detector_module.AppType
        else:
            raise ImportError("无法导入 BaseAppDetector 和 AppType")


'''

def fix_detector_file(file_path):
    """修复单个检测器文件"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 提取文件名作为检测器名称
        filename = os.path.basename(file_path)
        detector_name = filename.replace('.py', '').replace('_', ' ').title() + '检测器'
        
        # 查找类定义的开始
        lines = content.split('\n')
        class_start_index = -1
        
        for i, line in enumerate(lines):
            if line.strip().startswith('class ') and 'Detector' in line:
                class_start_index = i
                break
        
        if class_start_index == -1:
            print(f"❌ 未找到类定义: {file_path}")
            return False
        
        # 提取类定义及其后的内容
        class_content = '\n'.join(lines[class_start_index:])
        
        # 创建新的文件内容
        new_header = create_robust_import_header().format(detector_name=detector_name)
        new_content = new_header + class_content
        
        # 写回文件
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(new_content)
        
        print(f"✅ 修复成功: {filename}")
        return True
        
    except Exception as e:
        print(f"❌ 修复失败 {file_path}: {e}")
        return False

def fix_all_detectors():
    """修复所有检测器文件"""
    print("🔧 开始修复检测器导入路径问题...")
    
    # 获取当前目录
    current_dir = Path(__file__).parent
    detectors_dir = current_dir / "detectors"
    
    if not detectors_dir.exists():
        print(f"❌ 检测器目录不存在: {detectors_dir}")
        return
    
    # 查找所有检测器文件
    detector_files = list(detectors_dir.glob("*_detector.py"))
    
    if not detector_files:
        print("❌ 未找到检测器文件")
        return
    
    print(f"📁 找到 {len(detector_files)} 个检测器文件")
    
    success_count = 0
    
    for file_path in detector_files:
        # 跳过闹钟检测器，它有特殊的导入需求
        if 'alarm_detector.py' in str(file_path):
            print(f"⏭️ 跳过特殊文件: {file_path.name}")
            continue
            
        if fix_detector_file(str(file_path)):
            success_count += 1
    
    print(f"\n📊 修复结果:")
    print(f"  ✅ 成功修复: {success_count} 个文件")
    print(f"  📁 总文件数: {len(detector_files)} 个")
    
    if success_count == len(detector_files) - 1:  # -1 因为跳过了alarm_detector
        print("🎉 所有检测器文件修复完成！")
    else:
        print("⚠️ 部分文件修复失败，请检查错误信息")

def fix_alarm_detector():
    """特别修复闹钟检测器"""
    print("\n🔧 修复闹钟检测器...")
    
    current_dir = Path(__file__).parent
    alarm_file = current_dir / "detectors" / "alarm_detector.py"
    
    if not alarm_file.exists():
        print("❌ 闹钟检测器文件不存在")
        return
    
    try:
        with open(alarm_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 闹钟检测器的特殊导入
        alarm_import_header = '''"""
闹钟检测器
提供闹钟相关的检测和管理功能
"""
import re
import subprocess
from typing import List, Dict, Optional
import sys
import os

# 添加父目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

# 导入日志模块
try:
    from core.logger import log
except ImportError:
    # 简单的日志替代
    class SimpleLog:
        def info(self, msg): print(f"INFO: {msg}")
        def error(self, msg): print(f"ERROR: {msg}")
        def debug(self, msg): print(f"DEBUG: {msg}")
        def warning(self, msg): print(f"WARNING: {msg}")
    log = SimpleLog()

# 导入工具类
try:
    from pages.base.detectors.detector_utils import DetectorUtils
except ImportError:
    try:
        from pages.base.detectors.detector_utils import DetectorUtils
    except ImportError:
        # 如果都失败了，创建一个简单的替代
        class DetectorUtils:
            @staticmethod
            def execute_adb_command(command, timeout=10):
                try:
                    import subprocess
                    result = subprocess.run(command, capture_output=True, text=True, timeout=timeout)
                    return result.returncode == 0, result.stdout
                except:
                    return False, ""


'''
        
        # 查找类定义
        lines = content.split('\n')
        class_start_index = -1
        
        for i, line in enumerate(lines):
            if line.strip().startswith('class AlarmDetector'):
                class_start_index = i
                break
        
        if class_start_index != -1:
            class_content = '\n'.join(lines[class_start_index:])
            new_content = alarm_import_header + class_content
            
            with open(alarm_file, 'w', encoding='utf-8') as f:
                f.write(new_content)
            
            print("✅ 闹钟检测器修复成功")
        else:
            print("❌ 未找到闹钟检测器类定义")
            
    except Exception as e:
        print(f"❌ 修复闹钟检测器失败: {e}")

def main():
    """主函数"""
    print("🚀 开始修复所有检测器的导入路径问题")
    print("=" * 60)
    
    # 修复普通检测器
    fix_all_detectors()
    
    # 修复闹钟检测器
    fix_alarm_detector()
    
    print("\n" + "=" * 60)
    print("✅ 导入路径修复完成！")
    
    print("\n💡 修复内容:")
    print("  • 添加了多种导入策略")
    print("  • 支持相对导入和绝对导入")
    print("  • 添加了动态导入作为备用方案")
    print("  • 确保在各种环境下都能正常工作")

if __name__ == '__main__':
    main()
