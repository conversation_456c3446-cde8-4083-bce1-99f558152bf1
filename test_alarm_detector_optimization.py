#!/usr/bin/env python3
"""
测试优化后的闹钟检测器
验证当前闹钟未启动时返回False，有闹钟时返回True
"""
import sys
import time
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from pages.base.detectors.alarm_detector import AlarmDetector
from core.logger import log


def test_alarm_detector():
    """测试闹钟检测器的各种功能"""
    print("🔍 测试优化后的闹钟检测器")
    print("=" * 50)
    
    detector = AlarmDetector()
    
    # 测试1: 检查当前闹钟状态
    print("\n📱 测试1: 检查当前闹钟状态")
    try:
        alarm_status = detector.check_alarm_status()
        print(f"   闹钟状态: {'✅ 有闹钟' if alarm_status else '❌ 无闹钟'}")
        
        # 详细检查各个检测方法
        print("\n   详细检查:")
        has_next = detector._check_next_alarm()
        print(f"   - 系统下一个闹钟: {'✅' if has_next else '❌'}")
        
        has_db = detector._check_alarm_database()
        print(f"   - 数据库启用闹钟: {'✅' if has_db else '❌'}")
        
        has_process = detector._check_alarm_process()
        print(f"   - 活跃闹钟进程: {'✅' if has_process else '❌'}")
        
    except Exception as e:
        print(f"   ❌ 测试失败: {e}")
    
    # 测试2: 获取闹钟列表
    print("\n📋 测试2: 获取闹钟列表")
    try:
        alarms = detector.get_alarm_list()
        print(f"   找到 {len(alarms)} 个闹钟")
        
        if alarms:
            print("   闹钟详情:")
            for i, alarm in enumerate(alarms[:5], 1):  # 只显示前5个
                enabled_status = "启用" if alarm.get('enabled', False) else "禁用"
                time_str = alarm.get('time', 'N/A')
                label = alarm.get('label', '无标签')
                source = alarm.get('source', 'unknown')
                print(f"     {i}. {time_str} - {enabled_status} - {label} ({source})")
        else:
            print("   未找到任何闹钟")
            
    except Exception as e:
        print(f"   ❌ 测试失败: {e}")
    
    # 测试3: 获取下一个闹钟信息
    print("\n⏰ 测试3: 获取下一个闹钟信息")
    try:
        next_alarm = detector.get_next_alarm_info()
        if next_alarm:
            print(f"   下一个闹钟: {next_alarm}")
        else:
            print("   无下一个闹钟")
            
    except Exception as e:
        print(f"   ❌ 测试失败: {e}")
    
    # 测试4: 验证逻辑一致性
    print("\n🔍 测试4: 验证逻辑一致性")
    try:
        alarm_status = detector.check_alarm_status()
        alarms = detector.get_alarm_list()
        next_alarm = detector.get_next_alarm_info()
        
        # 检查逻辑一致性
        enabled_alarms = [a for a in alarms if a.get('enabled', False)]
        
        print(f"   闹钟状态: {alarm_status}")
        print(f"   启用的闹钟数: {len(enabled_alarms)}")
        print(f"   有下一个闹钟: {next_alarm is not None}")
        
        # 逻辑验证
        if alarm_status and len(enabled_alarms) == 0 and next_alarm is None:
            print("   ⚠️ 逻辑不一致: 状态为True但没有找到启用的闹钟")
        elif not alarm_status and (len(enabled_alarms) > 0 or next_alarm is not None):
            print("   ⚠️ 逻辑不一致: 状态为False但找到了启用的闹钟")
        else:
            print("   ✅ 逻辑一致")
            
    except Exception as e:
        print(f"   ❌ 测试失败: {e}")


def test_alarm_commands():
    """测试闹钟命令功能"""
    print("\n🛠️ 测试闹钟命令功能")
    print("=" * 30)
    
    detector = AlarmDetector()
    
    # 测试设置闹钟
    print("\n⏰ 测试设置闹钟 (8:30)")
    try:
        success = detector.set_alarm(8, 30)
        print(f"   设置结果: {'✅ 成功' if success else '❌ 失败'}")
        
        # 等待一下再检查状态
        time.sleep(2)
        
        # 检查设置后的状态
        alarm_status = detector.check_alarm_status()
        print(f"   设置后状态: {'✅ 有闹钟' if alarm_status else '❌ 无闹钟'}")
        
    except Exception as e:
        print(f"   ❌ 测试失败: {e}")


def main():
    """主测试函数"""
    print("🚀 闹钟检测器优化测试")
    print("=" * 60)
    
    # 基础功能测试
    test_alarm_detector()
    
    # 询问是否进行命令测试
    print("\n" + "=" * 60)
    response = input("是否进行闹钟设置命令测试? (y/N): ").strip().lower()
    
    if response in ['y', 'yes']:
        test_alarm_commands()
    else:
        print("跳过命令测试")
    
    print("\n" + "=" * 60)
    print("🎉 测试完成!")
    
    # 最终状态检查
    print("\n📊 最终状态检查:")
    detector = AlarmDetector()
    final_status = detector.check_alarm_status()
    print(f"   当前闹钟状态: {'✅ 有闹钟' if final_status else '❌ 无闹钟'}")
    
    if not final_status:
        print("   ✅ 优化成功: 当前无闹钟时正确返回False")
    else:
        print("   ℹ️ 当前有设置的闹钟")


if __name__ == '__main__':
    main()
